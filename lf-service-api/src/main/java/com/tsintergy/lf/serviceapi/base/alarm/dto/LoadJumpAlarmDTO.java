package com.tsintergy.lf.serviceapi.base.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 历史负荷相邻点跳变预警响应DTO
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@ApiModel("历史负荷相邻点跳变预警响应")
public class LoadJumpAlarmDTO {

    @ApiModelProperty("选择日期")
    private Date selectedDate;

    @ApiModelProperty("分析开始日期（向前推7天）")
    private Date analysisStartDate;

    @ApiModelProperty("分析结束日期")
    private Date analysisEndDate;

    @ApiModelProperty("跳变阈值（万兆瓦）")
    private Double jumpThreshold;

    @ApiModelProperty("负荷跳变告警列表")
    private List<LoadJumpDetailDTO> loadJumpAlarms;

    @ApiModelProperty("是否存在跳变告警")
    private Boolean hasJumpAlarm;

    @ApiModelProperty("总跳变次数")
    private Integer totalJumpCount;

    @ApiModelProperty("最大跳变值（万兆瓦）")
    private BigDecimal maxJumpValue;

    @ApiModelProperty("总体告警级别：1-正常，2-轻微，3-严重")
    private Integer overallAlarmLevel;

    @Data
    @ApiModel("负荷跳变详情")
    public static class LoadJumpDetailDTO {
        @ApiModelProperty("跳变日期")
        private Date jumpDate;

        @ApiModelProperty("跳变时段（前一个时段）")
        private Integer fromTimePoint;

        @ApiModelProperty("跳变时段（后一个时段）")
        private Integer toTimePoint;

        @ApiModelProperty("前一时段时间")
        private String fromTime;

        @ApiModelProperty("后一时段时间")
        private String toTime;

        @ApiModelProperty("前一时段负荷值（万兆瓦）")
        private BigDecimal fromLoadValue;

        @ApiModelProperty("后一时段负荷值（万兆瓦）")
        private BigDecimal toLoadValue;

        @ApiModelProperty("跳变值（万兆瓦）")
        private BigDecimal jumpValue;

        @ApiModelProperty("跳变方向：1-上升，-1-下降")
        private Integer jumpDirection;

        @ApiModelProperty("是否跨天跳变")
        private Boolean isCrossDayJump;

        @ApiModelProperty("告警级别：1-正常，2-轻微，3-严重")
        private Integer alarmLevel;

        @ApiModelProperty("告警信息")
        private String alarmMessage;
    }
}
