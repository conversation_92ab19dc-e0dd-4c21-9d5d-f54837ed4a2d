package com.tsintergy.lf.serviceapi.base.alarm.api;

import com.tsintergy.lf.serviceapi.base.alarm.dto.*;

import java.util.Date;

/**
 * 准确率提升告警服务接口
 * <AUTHOR>
 * @date 2025/6/19
 */
public interface AccuracyImprovementAlarmService {

    /**
     * 实际气象对比与预警
     * 根据选择日期展示对应的预测气象数据，同时展示选择日期近14天历史气象数据
     * 分析历史气象数据缺失情况并进行告警提示
     * 
     * @param cityId 城市ID
     * @param date 选择日期
     * @return 气象对比预警结果
     * @throws Exception
     */
    WeatherComparisonAlarmDTO getWeatherComparisonAlarm(String cityId, Date date) throws Exception;

    /**
     * 预测气象剧烈波动预警
     * 根据选择日期的预测气象数据极值，和历史前一天的实际气象数据的极值进行对比
     * 如果相同项超过某个阈值进行告警
     * 
     * @param cityId 城市ID
     * @param date 选择日期
     * @param temperatureThreshold 温度阈值
     * @return 气象波动预警结果
     * @throws Exception
     */
    WeatherVolatilityAlarmDTO getWeatherVolatilityAlarm(String cityId, Date date, Double temperatureThreshold) throws Exception;

    /**
     * 历史负荷相邻点跳变预警（30万兆瓦）
     * 根据选择日期向前推7天，进行历史负荷分析，判断288时段中相邻时刻的实际负荷发生跳变超过30万兆瓦进行预警
     * 
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param date 选择日期
     * @param jumpThreshold 跳变阈值（万兆瓦）
     * @return 负荷跳变预警结果
     * @throws Exception
     */
    LoadJumpAlarmDTO getLoadJumpAlarm(String cityId, String caliberId, Date date, Double jumpThreshold) throws Exception;

    /**
     * 预测后评估告警
     * 根据选择日期展示当月预测准确率平均值，同时展示同比数据
     * 
     * @param cityId 城市ID
     * @param caliberId 口径ID
     * @param date 选择日期
     * @return 准确率评估告警结果
     * @throws Exception
     */
    AccuracyEvaluationAlarmDTO getAccuracyEvaluationAlarm(String cityId, String caliberId, Date date) throws Exception;
}
