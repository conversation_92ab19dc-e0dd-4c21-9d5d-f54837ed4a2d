package com.tsintergy.lf.serviceapi.base.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实际气象对比与预警响应DTO
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@ApiModel("实际气象对比与预警响应")
public class WeatherComparisonAlarmDTO {

    @ApiModelProperty("选择日期")
    private Date selectedDate;

    @ApiModelProperty("预测气象数据")
    private WeatherDataDTO forecastWeather;

    @ApiModelProperty("历史气象数据列表（近14天）")
    private List<WeatherDataDTO> historicalWeatherList;

    @ApiModelProperty("数据缺失预警信息")
    private List<DataMissingAlarmDTO> dataMissingAlarms;

    @ApiModelProperty("参照历史负荷日期列表")
    private List<Date> referenceLoadDates;

    @ApiModelProperty("是否存在数据缺失")
    private Boolean hasDataMissing;

    @Data
    @ApiModel("气象数据")
    public static class WeatherDataDTO {
        @ApiModelProperty("日期")
        private Date date;

        @ApiModelProperty("最高温度")
        private BigDecimal highestTemperature;

        @ApiModelProperty("最低温度")
        private BigDecimal lowestTemperature;

        @ApiModelProperty("平均温度")
        private BigDecimal averageTemperature;

        @ApiModelProperty("最高湿度")
        private BigDecimal highestHumidity;

        @ApiModelProperty("最低湿度")
        private BigDecimal lowestHumidity;

        @ApiModelProperty("平均湿度")
        private BigDecimal averageHumidity;

        @ApiModelProperty("降雨量")
        private BigDecimal rainfall;

        @ApiModelProperty("风速")
        private BigDecimal windSpeed;

        @ApiModelProperty("风向")
        private BigDecimal windDirection;

        @ApiModelProperty("数据类型：1-预测，2-历史")
        private Integer dataType;
    }

    @Data
    @ApiModel("数据缺失告警")
    public static class DataMissingAlarmDTO {
        @ApiModelProperty("缺失日期")
        private Date missingDate;

        @ApiModelProperty("缺失字段列表")
        private List<String> missingFields;

        @ApiModelProperty("告警级别：1-轻微，2-严重")
        private Integer alarmLevel;

        @ApiModelProperty("告警信息")
        private String alarmMessage;
    }
}
