package com.tsintergy.lf.serviceapi.base.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预测气象剧烈波动预警响应DTO
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@ApiModel("预测气象剧烈波动预警响应")
public class WeatherVolatilityAlarmDTO {

    @ApiModelProperty("选择日期")
    private Date selectedDate;

    @ApiModelProperty("前一天日期")
    private Date previousDate;

    @ApiModelProperty("温度阈值")
    private Double temperatureThreshold;

    @ApiModelProperty("预测气象极值")
    private WeatherExtremeDTO forecastExtreme;

    @ApiModelProperty("前一天实际气象极值")
    private WeatherExtremeDTO previousActualExtreme;

    @ApiModelProperty("温度波动告警列表")
    private List<TemperatureVolatilityAlarmDTO> temperatureAlarms;

    @ApiModelProperty("是否存在剧烈波动")
    private Boolean hasVolatilityAlarm;

    @ApiModelProperty("总体告警级别：1-正常，2-轻微，3-严重")
    private Integer overallAlarmLevel;

    @Data
    @ApiModel("气象极值数据")
    public static class WeatherExtremeDTO {
        @ApiModelProperty("日期")
        private Date date;

        @ApiModelProperty("最高温度")
        private BigDecimal highestTemperature;

        @ApiModelProperty("最低温度")
        private BigDecimal lowestTemperature;

        @ApiModelProperty("平均温度")
        private BigDecimal averageTemperature;

        @ApiModelProperty("数据类型：1-预测，2-历史")
        private Integer dataType;
    }

    @Data
    @ApiModel("温度波动告警")
    public static class TemperatureVolatilityAlarmDTO {
        @ApiModelProperty("温度类型：1-最高温度，2-最低温度，3-平均温度")
        private Integer temperatureType;

        @ApiModelProperty("温度类型名称")
        private String temperatureTypeName;

        @ApiModelProperty("预测值")
        private BigDecimal forecastValue;

        @ApiModelProperty("前一天实际值")
        private BigDecimal previousActualValue;

        @ApiModelProperty("温度差值")
        private BigDecimal temperatureDifference;

        @ApiModelProperty("是否超过阈值")
        private Boolean exceedsThreshold;

        @ApiModelProperty("告警级别：1-正常，2-轻微，3-严重")
        private Integer alarmLevel;

        @ApiModelProperty("告警信息")
        private String alarmMessage;
    }
}
