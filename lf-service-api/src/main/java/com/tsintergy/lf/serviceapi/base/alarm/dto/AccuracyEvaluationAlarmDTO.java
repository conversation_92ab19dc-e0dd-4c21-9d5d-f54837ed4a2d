package com.tsintergy.lf.serviceapi.base.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预测后评估告警响应DTO
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@ApiModel("预测后评估告警响应")
public class AccuracyEvaluationAlarmDTO {

    @ApiModelProperty("选择日期")
    private Date selectedDate;

    @ApiModelProperty("当前年份")
    private Integer currentYear;

    @ApiModelProperty("当前月份")
    private Integer currentMonth;

    @ApiModelProperty("当前日期")
    private Integer currentDay;

    @ApiModelProperty("当年数据")
    private AccuracyDataDTO currentYearData;

    @ApiModelProperty("同比数据（去年同期）")
    private AccuracyDataDTO lastYearData;

    @ApiModelProperty("同比变化")
    private AccuracyComparisonDTO yearOverYearComparison;

    @ApiModelProperty("是否存在准确率告警")
    private Boolean hasAccuracyAlarm;

    @ApiModelProperty("告警级别：1-正常，2-轻微，3-严重")
    private Integer alarmLevel;

    @ApiModelProperty("告警信息")
    private String alarmMessage;

    @Data
    @ApiModel("准确率数据")
    public static class AccuracyDataDTO {
        @ApiModelProperty("年份")
        private Integer year;

        @ApiModelProperty("月份")
        private Integer month;

        @ApiModelProperty("分析天数")
        private Integer analysisDays;

        @ApiModelProperty("预测准确率平均值（%）")
        private BigDecimal averageAccuracy;

        @ApiModelProperty("最大负荷预测准确率平均值（%）")
        private BigDecimal maxLoadAccuracy;

        @ApiModelProperty("最小负荷预测准确率平均值（%）")
        private BigDecimal minLoadAccuracy;

        @ApiModelProperty("96点预测准确率平均值（%）")
        private BigDecimal pointAccuracy;

        @ApiModelProperty("数据完整性（%）")
        private BigDecimal dataCompleteness;
    }

    @Data
    @ApiModel("准确率同比对比")
    public static class AccuracyComparisonDTO {
        @ApiModelProperty("准确率变化（百分点）")
        private BigDecimal accuracyChange;

        @ApiModelProperty("最大负荷准确率变化（百分点）")
        private BigDecimal maxLoadAccuracyChange;

        @ApiModelProperty("最小负荷准确率变化（百分点）")
        private BigDecimal minLoadAccuracyChange;

        @ApiModelProperty("96点准确率变化（百分点）")
        private BigDecimal pointAccuracyChange;

        @ApiModelProperty("变化趋势：1-提升，0-持平，-1-下降")
        private Integer trend;

        @ApiModelProperty("变化描述")
        private String changeDescription;
    }
}
