package com.tsintergy.lf.web.base.alarm.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.alarm.api.AccuracyImprovementAlarmService;
import com.tsintergy.lf.serviceapi.base.alarm.dto.*;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/** @Description
 * 准确率提升告警
 * <AUTHOR>
 * @Date 2025/6/19 09:36
 **/
@RestController
@RequestMapping("/accuracy-improvement-alarm")
@Api(tags = "准确率提升告警")
public class AccuracyImprovementAlarmController extends BaseController {

    @Autowired
    private AccuracyImprovementAlarmService accuracyImprovementAlarmService;

    /**
     * 实际气象对比与预警
     * 根据选择日期展示对应的预测气象数据，同时展示选择日期近14天历史气象数据
     * 分析历史气象数据缺失情况并进行告警提示
     */
    @GetMapping("/weather-comparison")
    @ApiOperation("实际气象对比与预警")
    public BaseResp getWeatherComparisonAlarm(
            @ApiParam(value = "选择日期", required = true) @RequestParam Date date) {
        try {
            String cityId = getLoginCityId();
            WeatherComparisonAlarmDTO result = accuracyImprovementAlarmService.getWeatherComparisonAlarm(cityId, date);
            return baseResp(result);
        } catch (Exception e) {
            logger.error("实际气象对比与预警查询失败", e);
            return getFailResp("查询失败：" + e.getMessage());
        }
    }

    /**
     * 预测气象剧烈波动预警
     * 根据选择日期的预测气象数据极值，和历史前一天的实际气象数据的极值进行对比
     * 如果相同项超过某个阈值进行告警
     */
    @GetMapping("/weather-volatility")
    @ApiOperation("预测气象剧烈波动预警")
    public BaseResp getWeatherVolatilityAlarm(
            @ApiParam(value = "选择日期", required = true) @RequestParam Date date,
            @ApiParam(value = "温度阈值", required = false, defaultValue = "4.0") @RequestParam(defaultValue = "4.0") Double temperatureThreshold) {
        try {
            String cityId = getLoginCityId();
            WeatherVolatilityAlarmDTO result = accuracyImprovementAlarmService.getWeatherVolatilityAlarm(cityId, date, temperatureThreshold);
            return baseResp(result);
        } catch (Exception e) {
            logger.error("预测气象剧烈波动预警查询失败", e);
            return getFailResp("查询失败：" + e.getMessage());
        }
    }

    /**
     * 历史负荷相邻点跳变预警（30万兆瓦）
     * 根据选择日期向前推7天，进行历史负荷分析，判断288时段中相邻时刻的实际负荷发生跳变超过30万兆瓦进行预警
     */
    @GetMapping("/load-jump")
    @ApiOperation("历史负荷相邻点跳变预警")
    public BaseResp getLoadJumpAlarm(
            @ApiParam(value = "选择日期", required = true) @RequestParam Date date,
            @ApiParam(value = "跳变阈值（万兆瓦）", required = false, defaultValue = "30.0") @RequestParam(defaultValue = "30.0") Double jumpThreshold) {
        try {
            String cityId = getLoginCityId();
            String caliberId = getCaliberId();
            LoadJumpAlarmDTO result = accuracyImprovementAlarmService.getLoadJumpAlarm(cityId, caliberId, date, jumpThreshold);
            return baseResp(result);
        } catch (Exception e) {
            logger.error("历史负荷相邻点跳变预警查询失败", e);
            return getFailResp("查询失败：" + e.getMessage());
        }
    }

    /**
     * 预测后评估告警
     * 根据选择日期展示当月预测准确率平均值，同时展示同比数据
     */
    @GetMapping("/accuracy-evaluation")
    @ApiOperation("预测后评估告警")
    public BaseResp getAccuracyEvaluationAlarm(
            @ApiParam(value = "选择日期", required = true) @RequestParam Date date) {
        try {
            String cityId = getLoginCityId();
            String caliberId = getCaliberId();
            AccuracyEvaluationAlarmDTO result = accuracyImprovementAlarmService.getAccuracyEvaluationAlarm(cityId, caliberId, date);
            return baseResp(result);
        } catch (Exception e) {
            logger.error("预测后评估告警查询失败", e);
            return getFailResp("查询失败：" + e.getMessage());
        }
    }
}
