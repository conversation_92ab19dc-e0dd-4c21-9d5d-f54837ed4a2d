package com.tsintergy.lf.serviceimpl.alarm.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.alarm.api.AccuracyImprovementAlarmService;
import com.tsintergy.lf.serviceapi.base.alarm.dto.*;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHis288Service;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 准确率提升告警服务实现
 * <AUTHOR>
 * @date 2025/6/19
 */
@Service
@Slf4j
public class AccuracyImprovementAlarmServiceImpl implements AccuracyImprovementAlarmService {

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadCityHis288Service loadCityHis288Service;

    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;

    @Override
    public WeatherComparisonAlarmDTO getWeatherComparisonAlarm(String cityId, java.util.Date date) throws Exception {
        WeatherComparisonAlarmDTO result = new WeatherComparisonAlarmDTO();
        result.setSelectedDate(date);

        // 1. 获取选择日期的预测气象数据
        WeatherFeatureCityDayFcDO forecastWeather = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDO(cityId, date);
        if (forecastWeather != null) {
            result.setForecastWeather(convertToWeatherDataDTO(forecastWeather, 1));
        }

        // 2. 获取近14天历史气象数据
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -14);
        java.util.Date startDate = cal.getTime();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        java.util.Date endDate = cal.getTime();

        List<WeatherFeatureCityDayHisDO> historicalWeatherList = weatherFeatureCityDayHisService
                .findWeatherFeatureCityDayHisDO(Arrays.asList(cityId), startDate, endDate);

        List<WeatherComparisonAlarmDTO.WeatherDataDTO> historicalWeatherDTOList = new ArrayList<>();
        List<WeatherComparisonAlarmDTO.DataMissingAlarmDTO> dataMissingAlarms = new ArrayList<>();

        // 3. 检查数据缺失并生成告警
        List<java.util.Date> expectedDates = getDateRange(startDate, endDate);
        Map<java.util.Date, WeatherFeatureCityDayHisDO> weatherMap = historicalWeatherList.stream()
                .collect(Collectors.toMap(w -> new java.util.Date(w.getDate().getTime()), w -> w, (o, n) -> n));

        for (java.util.Date expectedDate : expectedDates) {
            WeatherFeatureCityDayHisDO weather = weatherMap.get(expectedDate);
            if (weather != null) {
                historicalWeatherDTOList.add(convertToWeatherDataDTO(weather, 2));
                // 检查字段缺失
                List<String> missingFields = checkMissingFields(weather);
                if (!missingFields.isEmpty()) {
                    WeatherComparisonAlarmDTO.DataMissingAlarmDTO alarm = new WeatherComparisonAlarmDTO.DataMissingAlarmDTO();
                    alarm.setMissingDate(expectedDate);
                    alarm.setMissingFields(missingFields);
                    alarm.setAlarmLevel(missingFields.size() > 3 ? 2 : 1);
                    alarm.setAlarmMessage(String.format("日期 %s 存在 %d 个气象字段缺失：%s", 
                            DateUtil.getDateToStrFORMAT(expectedDate, "yyyy-MM-dd"), 
                            missingFields.size(), 
                            String.join(", ", missingFields)));
                    dataMissingAlarms.add(alarm);
                }
            } else {
                // 完全缺失数据
                WeatherComparisonAlarmDTO.DataMissingAlarmDTO alarm = new WeatherComparisonAlarmDTO.DataMissingAlarmDTO();
                alarm.setMissingDate(expectedDate);
                alarm.setMissingFields(Arrays.asList("所有气象数据"));
                alarm.setAlarmLevel(2);
                alarm.setAlarmMessage(String.format("日期 %s 完全缺失气象数据", 
                        DateUtil.getDateToStrFORMAT(expectedDate, "yyyy-MM-dd")));
                dataMissingAlarms.add(alarm);
            }
        }

        result.setHistoricalWeatherList(historicalWeatherDTOList);
        result.setDataMissingAlarms(dataMissingAlarms);
        result.setHasDataMissing(!dataMissingAlarms.isEmpty());

        // 4. 根据气象条件锚定参照历史负荷日期
        if (forecastWeather != null) {
            List<java.util.Date> referenceDates = findReferenceLoadDates(forecastWeather, historicalWeatherList);
            result.setReferenceLoadDates(referenceDates);
        }

        return result;
    }

    @Override
    public WeatherVolatilityAlarmDTO getWeatherVolatilityAlarm(String cityId, java.util.Date date, Double temperatureThreshold) throws Exception {
        WeatherVolatilityAlarmDTO result = new WeatherVolatilityAlarmDTO();
        result.setSelectedDate(date);
        result.setTemperatureThreshold(temperatureThreshold);

        // 1. 获取选择日期的预测气象极值
        WeatherFeatureCityDayFcDO forecastWeather = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDO(cityId, date);
        if (forecastWeather != null) {
            WeatherVolatilityAlarmDTO.WeatherExtremeDTO forecastExtreme = new WeatherVolatilityAlarmDTO.WeatherExtremeDTO();
            forecastExtreme.setDate(date);
            forecastExtreme.setHighestTemperature(forecastWeather.getHighestTemperature());
            forecastExtreme.setLowestTemperature(forecastWeather.getLowestTemperature());
            forecastExtreme.setAverageTemperature(forecastWeather.getAveTemperature());
            forecastExtreme.setDataType(1);
            result.setForecastExtreme(forecastExtreme);
        }

        // 2. 获取前一天的实际气象极值
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        java.util.Date previousDate = cal.getTime();
        result.setPreviousDate(previousDate);

        WeatherFeatureCityDayHisDO previousWeather = weatherFeatureCityDayHisService.getWeatherFeatureCityDayHisDO(cityId, previousDate);
        if (previousWeather != null) {
            WeatherVolatilityAlarmDTO.WeatherExtremeDTO previousExtreme = new WeatherVolatilityAlarmDTO.WeatherExtremeDTO();
            previousExtreme.setDate(previousDate);
            previousExtreme.setHighestTemperature(previousWeather.getHighestTemperature());
            previousExtreme.setLowestTemperature(previousWeather.getLowestTemperature());
            previousExtreme.setAverageTemperature(previousWeather.getAveTemperature());
            previousExtreme.setDataType(2);
            result.setPreviousActualExtreme(previousExtreme);
        }

        // 3. 分析温度波动并生成告警
        List<WeatherVolatilityAlarmDTO.TemperatureVolatilityAlarmDTO> temperatureAlarms = new ArrayList<>();
        int maxAlarmLevel = 1;

        if (forecastWeather != null && previousWeather != null) {
            // 检查最高温度
            WeatherVolatilityAlarmDTO.TemperatureVolatilityAlarmDTO highestTempAlarm = 
                    analyzeTemperatureVolatility(1, "最高温度", 
                            forecastWeather.getHighestTemperature(), 
                            previousWeather.getHighestTemperature(), 
                            temperatureThreshold);
            temperatureAlarms.add(highestTempAlarm);
            maxAlarmLevel = Math.max(maxAlarmLevel, highestTempAlarm.getAlarmLevel());

            // 检查最低温度
            WeatherVolatilityAlarmDTO.TemperatureVolatilityAlarmDTO lowestTempAlarm = 
                    analyzeTemperatureVolatility(2, "最低温度", 
                            forecastWeather.getLowestTemperature(), 
                            previousWeather.getLowestTemperature(), 
                            temperatureThreshold);
            temperatureAlarms.add(lowestTempAlarm);
            maxAlarmLevel = Math.max(maxAlarmLevel, lowestTempAlarm.getAlarmLevel());

            // 检查平均温度
            WeatherVolatilityAlarmDTO.TemperatureVolatilityAlarmDTO avgTempAlarm = 
                    analyzeTemperatureVolatility(3, "平均温度", 
                            forecastWeather.getAveTemperature(), 
                            previousWeather.getAveTemperature(), 
                            temperatureThreshold);
            temperatureAlarms.add(avgTempAlarm);
            maxAlarmLevel = Math.max(maxAlarmLevel, avgTempAlarm.getAlarmLevel());
        }

        result.setTemperatureAlarms(temperatureAlarms);
        result.setHasVolatilityAlarm(maxAlarmLevel > 1);
        result.setOverallAlarmLevel(maxAlarmLevel);

        return result;
    }

    @Override
    public LoadJumpAlarmDTO getLoadJumpAlarm(String cityId, String caliberId, java.util.Date date, Double jumpThreshold) throws Exception {
        LoadJumpAlarmDTO result = new LoadJumpAlarmDTO();
        result.setSelectedDate(date);
        result.setJumpThreshold(jumpThreshold);

        // 1. 计算分析日期范围（向前推7天）
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -7);
        java.util.Date startDate = cal.getTime();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        java.util.Date endDate = cal.getTime();

        result.setAnalysisStartDate(startDate);
        result.setAnalysisEndDate(endDate);

        // 2. 获取历史负荷数据（288点）
        List<LoadCityHis288DO> loadDataList = loadCityHis288Service.getLoadCityHisDO(cityId, startDate, endDate, caliberId);

        // 3. 分析相邻点跳变
        List<LoadJumpAlarmDTO.LoadJumpDetailDTO> jumpAlarms = new ArrayList<>();
        BigDecimal maxJumpValue = BigDecimal.ZERO;
        int maxAlarmLevel = 1;

        for (LoadCityHis288DO loadData : loadDataList) {
            List<LoadJumpAlarmDTO.LoadJumpDetailDTO> dayJumpAlarms = analyzeLoadJumps(loadData, jumpThreshold);
            jumpAlarms.addAll(dayJumpAlarms);
            
            for (LoadJumpAlarmDTO.LoadJumpDetailDTO jumpAlarm : dayJumpAlarms) {
                if (jumpAlarm.getJumpValue().abs().compareTo(maxJumpValue) > 0) {
                    maxJumpValue = jumpAlarm.getJumpValue().abs();
                }
                maxAlarmLevel = Math.max(maxAlarmLevel, jumpAlarm.getAlarmLevel());
            }
        }

        // 4. 检查跨天跳变
        if (loadDataList.size() > 1) {
            List<LoadJumpAlarmDTO.LoadJumpDetailDTO> crossDayJumps = analyzeCrossDayJumps(loadDataList, jumpThreshold);
            jumpAlarms.addAll(crossDayJumps);
            
            for (LoadJumpAlarmDTO.LoadJumpDetailDTO jumpAlarm : crossDayJumps) {
                if (jumpAlarm.getJumpValue().abs().compareTo(maxJumpValue) > 0) {
                    maxJumpValue = jumpAlarm.getJumpValue().abs();
                }
                maxAlarmLevel = Math.max(maxAlarmLevel, jumpAlarm.getAlarmLevel());
            }
        }

        result.setLoadJumpAlarms(jumpAlarms);
        result.setHasJumpAlarm(!jumpAlarms.isEmpty());
        result.setTotalJumpCount(jumpAlarms.size());
        result.setMaxJumpValue(maxJumpValue);
        result.setOverallAlarmLevel(maxAlarmLevel);

        return result;
    }

    @Override
    public AccuracyEvaluationAlarmDTO getAccuracyEvaluationAlarm(String cityId, String caliberId, java.util.Date date) throws Exception {
        AccuracyEvaluationAlarmDTO result = new AccuracyEvaluationAlarmDTO();
        result.setSelectedDate(date);

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int currentYear = cal.get(Calendar.YEAR);
        int currentMonth = cal.get(Calendar.MONTH) + 1;
        int currentDay = cal.get(Calendar.DAY_OF_MONTH);

        result.setCurrentYear(currentYear);
        result.setCurrentMonth(currentMonth);
        result.setCurrentDay(currentDay);

        // 1. 计算当年数据
        AccuracyEvaluationAlarmDTO.AccuracyDataDTO currentYearData = calculateAccuracyData(cityId, caliberId, currentYear, currentMonth, currentDay);
        result.setCurrentYearData(currentYearData);

        // 2. 计算去年同期数据
        AccuracyEvaluationAlarmDTO.AccuracyDataDTO lastYearData = calculateAccuracyData(cityId, caliberId, currentYear - 1, currentMonth, currentDay);
        result.setLastYearData(lastYearData);

        // 3. 计算同比变化
        if (currentYearData != null && lastYearData != null) {
            AccuracyEvaluationAlarmDTO.AccuracyComparisonDTO comparison = calculateYearOverYearComparison(currentYearData, lastYearData);
            result.setYearOverYearComparison(comparison);
        }

        // 4. 判断是否需要告警
        int alarmLevel = 1;
        String alarmMessage = "预测准确率正常";
        
        if (currentYearData != null) {
            if (currentYearData.getAverageAccuracy() != null && currentYearData.getAverageAccuracy().compareTo(new BigDecimal("80")) < 0) {
                alarmLevel = 3;
                alarmMessage = "当月预测准确率低于80%，需要重点关注";
            } else if (currentYearData.getAverageAccuracy() != null && currentYearData.getAverageAccuracy().compareTo(new BigDecimal("90")) < 0) {
                alarmLevel = 2;
                alarmMessage = "当月预测准确率低于90%，建议关注";
            }
        }

        result.setHasAccuracyAlarm(alarmLevel > 1);
        result.setAlarmLevel(alarmLevel);
        result.setAlarmMessage(alarmMessage);

        return result;
    }

    // 辅助方法实现将在下一步继续...
}
